# ===== Python 相关 =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
.pids/

# ===== Node.js / React 相关 =====
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build output
/frontend/build

# ===== 操作系统相关 =====
# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# ===== 项目特定文件 =====
# 上传文件目录
/backend/uploads/*
!/backend/uploads/.gitkeep

# 生成结果目录
/backend/results/*
!/backend/results/.gitkeep

# 日志文件
*.log
logs/
